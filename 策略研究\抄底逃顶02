//------------------------------------------------------------------------
// 简称: SZP4_2_Long
// 名称: 
// 类别: 公式应用
// 类型: 用户应用
// 输出: Void
//------------------------------------------------------------------------
Params
	//此处添加参数
	Numeric BarNo(14);  // 周期
	Numeric DnBand(30) ;  // 下限
	Numeric TS(35);//移动止损止盈幅度
	Numeric StartPro1(5);  //1级跟踪止盈，盈利5%启动
	Numeric StopPro1(20); //1级跟踪止盈，盈利回撤20%触发
	
	Numeric StartPro2(10); //2级跟踪止盈，盈利10%启动
	Numeric StopPro2(10);  //2级跟踪止盈，盈利回撤30%触发
	Numeric TradeEndTime(1455);// 交易结束时间(14:55)
    
Vars
	//此处添加变量
	Series<Numeric> SumBar(0);
	Series<Numeric> Zscore(0);
	Series<Numeric> Tscore(0);
	Series<Numeric> QStick(0);
	Series<Numeric> IFT_IMI(0);
	Series<Numeric> IMI(0);
	Series<Numeric> pre_imi;
	Series<Numeric> Strength(0);
	Series<Numeric> pre_strength;
	Series<Numeric> pre_strength2;
	Series<bool> cd_con;
	Series<bool> cd_exit_con;
		Series<Numeric> HigherAfterEntry;
	Series<Numeric> LowerAfterEntry;
	Series<Numeric> HighAfterEntry;
	Series<Numeric> LowAfterEntry;
	Series<Numeric> liQKA;
	Series<Numeric> DliqPoint;
	Series<Numeric> KliqPoint;
	Series<Numeric> entry_price;
	

		
	
Events
	//此处实现事件函数
	
	//初始化事件函数，策略运行期间，首先运行且只有一次
	OnInit()
	{
		//=========数据源相关设置==============
		AddDataFlag(Enum_Data_RolloverBackWard());	//设置后复权

		AddDataFlag(Enum_Data_RolloverRealPrice());	//设置映射真实价格

		AddDataFlag(Enum_Data_AutoSwapPosition());	//设置自动换仓

	}


	//Bar更新事件函数，参数indexs表示变化的数据源图层ID数组
	OnBar(ArrayRef<Integer> indexs)
	{
        If(BarsSinceentry == 0)
		{
			HighAfterEntry = High;
			//LowAfterEntry = Low;
		}
		else
		{
			HighAfterEntry = Max(HighAfterEntry,High); // 记录下当前Bar的最高点，用于下一个Bar的跟踪止损判断
			//LowAfterEntry = Min(LowAfterEntry,Low);    // 记录下当前Bar的最低点，用于下一个Bar的跟踪止损判断
		}
		
		pre_imi = Summation(abs(Close-Open),BarNo) ;
		if (pre_imi <> 0)
		{
			IMI = (Summation(iif(Close > Open ,Close-Open,0),BarNo)/pre_imi)*100 ;
		} 
		PlotNumeric("IMI",IMI);
		PlotNumeric("DnBand",DnBand);
		cd_con = crossover(IMI, DnBand);
		if (cd_con[1] and MarketPosition<>1)
		{
			entry_price = open;
			buy(10, Open);
			LowerAfterEntry = entry_price;//保存多头开仓价格;
		}
		
		
		//********************************************************************
		If(MarketPosition == 1 && BarsSinceentry == 0)
    	{
    		//HigherAfterEntry = HigherAfterEntry[1];	
    		LowerAfterEntry	=MAX(LowerAfterEntry, Low);		
    	}
    	else If(MarketPosition == -1 && BarsSinceentry == 0)
    	{	
    		//HigherAfterEntry = MIN(HigherAfterEntry,High);	
    		LowerAfterEntry = LowerAfterEntry[1];	
    				
    	}
    	else if((MarketPosition != 0 && BarsSinceentry >= 1)) 
    	{
    		//HigherAfterEntry = MIN(HigherAfterEntry[1], High);		
    		LowerAfterEntry = MAX(LowerAfterEntry[1], Low);		
    	}
    	Commentary("Higherafterentry"+text(Higherafterentry));
    	Commentary("Lowerafterentry"+text(Lowerafterentry));
    	
    	
    	
    	If(MarketPosition == 0)   // 自适应参数默认值；
    	{
    		liQKA = 1;
    	}Else					 //当有持仓的情况下，liQKA会随着持仓时间的增加而逐渐减小，即止损止盈幅度乘数的减少。
    	{
    		liQKA = liQKA - 0.1; 
    		liQKA = Max(liQKA,0.5);
    	}
    	if(MarketPosition>0)
    	{
			DliqPoint = LowerAfterEntry - (Open*TS/1000)*liQKA; 
			//经过计算，这根吊灯出场线会随着持仓时间的增加变的越来越敏感；
    	}

    	 //PlotNumeric("DliqPoint[1]",DliqPoint[1]);
		//********************************************************************
		// 跟踪止盈止损
		if (MarketPosition==1 and low<=DliqPoint[1] And BarsSinceEntry >0  )
		{
			Sell(0, min(Open, DliqPoint[1]));
		}

		If(MarketPosition==1 && BarsSinceLastEntry>0 && HighAfterEntry[1]>=entry_price*(1+0.01*StartPro2) 
		&& Low<=HighAfterEntry[1]-(HighAfterEntry[1]-entry_price)*0.01*StopPro2) 
		{
			Sell(0,Min(Open,HighAfterEntry[1]-(HighAfterEntry[1]-entry_price)*0.01*StopPro2));
			Commentary("最大盈利达到"+Text(StartPro2)+"%之后盈利回撤"+Text(StopPro2)+"%平多");
			PlotString("Trilling2","Trilling2",high*1.01,red);
		}

		If(MarketPosition==1 && BarsSinceLastEntry>0 && HighAfterEntry[1]>=entry_price*(1+0.01*StartPro1) 
		&& Low<=HighAfterEntry[1]-(HighAfterEntry[1]-entry_price)*0.01*StopPro1) 
		{
			Sell(0,Min(Open,HighAfterEntry[1]-(HighAfterEntry[1]-entry_price)*0.01*StopPro1));
			Commentary("最大盈利达到"+Text(StartPro1)+"%之后盈利回撤"+Text(StopPro1)+"%平多");
			PlotString("Trilling1","Trilling1",high*1.01,red);
		}
		
		If(MarketPosition==1 && BarsSinceLastEntry>0 && HighAfterEntry[1]>=entry_price*(1+0.01*StartPro2))
		{
			PlotString("Trilling2_ready","Trilling2_ready",high*1.01,red);
			Commentary("最大盈利达到"+Text(StartPro2));
		} 
		
		If(MarketPosition==1 && BarsSinceLastEntry>0 && HighAfterEntry[1]>=entry_price*(1+0.01*StartPro1))
		{
			PlotString("Trilling1_ready","Trilling1_ready",high*1.01,red);
			Commentary("最大盈利达到"+Text(StartPro1));
		} 
 // 3. 收盘平仓
            If(CurrentTime >= TradeEndTime)
            {
                Sell(0, C);
            }
		// 指标出场
		// 指标出场效果不好，作为给大家的学习路径吧，我就保留不删这段代码了
		/*cd_exit_con = CrossOver(IMI, UpBand);
		if (cd_exit_con[1])
		{
			Sell(0, open);
		}*/
		
		
		/*pre_strength = Summation(abs(IMI-50),BarNo) ;
		pre_strength2 = Summation(iif((IMI-50)>0,(IMI-50),0),BarNo) ;
		if (pre_strength <> 0)
		{
			Strength = pre_strength2/pre_strength * 100 ;
		}*/
		
		/*PlotNumeric("IMI",IMI) ;
		PlotNumeric("Zero",0) ;
		PlotNumeric("UpBand",UpBand) ;
		PlotNumeric("DnBand",DnBand) ;*/

	}

//------------------------------------------------------------------------
// 编译版本	2022/09/13 111205
// 版权所有	hitlerls
// 更改声明	TradeBlazer Software保留对TradeBlazer平台
//			每一版本的TradeBlazer公式修改和重写的权利
//------------------------------------------------------------------------